// 图表管理类
class ChartManager {
    constructor() {
        this.charts = {};
    }

    // 创建资金曲线图
    createEquityCurve(canvasId, data) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        // 销毁已存在的图表
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const equityData = data.equityCurve || [];
        const labels = equityData.map(item => item.date);
        const values = equityData.map(item => item.balance);

        this.charts[canvasId] = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: '账户余额',
                    data: values,
                    borderColor: '#3498db',
                    backgroundColor: 'rgba(52, 152, 219, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '资金曲线'
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '日期'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '余额 (USD)'
                        },
                        beginAtZero: false
                    }
                }
            }
        });

        return this.charts[canvasId];
    }

    // 创建盈亏分布饼图
    createPnLDistribution(canvasId, stats) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const trades = window.db.getTrades();
        const longPnL = trades.filter(t => t.trade_type === 'long').reduce((sum, t) => sum + t.pnl, 0);
        const shortPnL = trades.filter(t => t.trade_type === 'short').reduce((sum, t) => sum + t.pnl, 0);

        this.charts[canvasId] = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['做多盈亏', '做空盈亏'],
                datasets: [{
                    data: [longPnL, shortPnL],
                    backgroundColor: [
                        longPnL >= 0 ? '#27ae60' : '#e74c3c',
                        shortPnL >= 0 ? '#27ae60' : '#e74c3c'
                    ],
                    borderWidth: 2,
                    borderColor: '#fff'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '做多/做空盈亏分布'
                    },
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });

        return this.charts[canvasId];
    }

    // 创建月度盈亏柱状图
    createMonthlyPnL(canvasId) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const trades = window.db.getTrades();
        const monthlyData = {};

        trades.forEach(trade => {
            const date = new Date(trade.trade_date);
            const monthKey = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
            
            if (!monthlyData[monthKey]) {
                monthlyData[monthKey] = 0;
            }
            monthlyData[monthKey] += trade.pnl;
        });

        const sortedMonths = Object.keys(monthlyData).sort();
        const values = sortedMonths.map(month => monthlyData[month]);

        this.charts[canvasId] = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: sortedMonths,
                datasets: [{
                    label: '月度盈亏',
                    data: values,
                    backgroundColor: values.map(v => v >= 0 ? '#27ae60' : '#e74c3c'),
                    borderColor: values.map(v => v >= 0 ? '#229954' : '#c0392b'),
                    borderWidth: 1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    title: {
                        display: true,
                        text: '月度盈亏统计'
                    },
                    legend: {
                        display: false
                    }
                },
                scales: {
                    x: {
                        title: {
                            display: true,
                            text: '月份'
                        }
                    },
                    y: {
                        title: {
                            display: true,
                            text: '盈亏 (USD)'
                        },
                        beginAtZero: true
                    }
                }
            }
        });

        return this.charts[canvasId];
    }

    // 创建盈亏日历热力图（简化版）
    createPnLCalendar(containerId) {
        const container = document.getElementById(containerId);
        if (!container) return;

        const dailyPnL = window.db.getDailyPnL();
        const currentYear = new Date().getFullYear();
        
        // 清空容器
        container.innerHTML = '';
        
        // 创建标题
        const title = document.createElement('h3');
        title.textContent = `${currentYear}年盈亏日历`;
        title.style.textAlign = 'center';
        title.style.marginBottom = '20px';
        container.appendChild(title);

        // 创建日历网格
        const calendar = document.createElement('div');
        calendar.style.display = 'grid';
        calendar.style.gridTemplateColumns = 'repeat(7, 1fr)';
        calendar.style.gap = '2px';
        calendar.style.maxWidth = '500px';
        calendar.style.margin = '0 auto';

        // 添加星期标题
        const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        weekdays.forEach(day => {
            const dayHeader = document.createElement('div');
            dayHeader.textContent = day;
            dayHeader.style.textAlign = 'center';
            dayHeader.style.fontWeight = 'bold';
            dayHeader.style.padding = '5px';
            calendar.appendChild(dayHeader);
        });

        // 获取当前月份的天数和起始日期
        const now = new Date();
        const firstDay = new Date(currentYear, now.getMonth(), 1);
        const lastDay = new Date(currentYear, now.getMonth() + 1, 0);
        const startDate = new Date(firstDay);
        startDate.setDate(startDate.getDate() - firstDay.getDay());

        // 生成日历格子
        for (let i = 0; i < 42; i++) { // 6周 * 7天
            const date = new Date(startDate);
            date.setDate(startDate.getDate() + i);
            
            const dayCell = document.createElement('div');
            dayCell.style.width = '30px';
            dayCell.style.height = '30px';
            dayCell.style.display = 'flex';
            dayCell.style.alignItems = 'center';
            dayCell.style.justifyContent = 'center';
            dayCell.style.fontSize = '12px';
            dayCell.style.border = '1px solid #ddd';
            dayCell.style.cursor = 'pointer';
            
            const dateStr = date.toISOString().split('T')[0];
            const pnl = dailyPnL[dateStr] || 0;
            
            dayCell.textContent = date.getDate();
            
            // 设置颜色
            if (date.getMonth() !== now.getMonth()) {
                dayCell.style.color = '#ccc';
                dayCell.style.backgroundColor = '#f9f9f9';
            } else if (pnl > 0) {
                dayCell.style.backgroundColor = `rgba(39, 174, 96, ${Math.min(Math.abs(pnl) / 1000, 1)})`;
                dayCell.style.color = 'white';
            } else if (pnl < 0) {
                dayCell.style.backgroundColor = `rgba(231, 76, 60, ${Math.min(Math.abs(pnl) / 1000, 1)})`;
                dayCell.style.color = 'white';
            } else {
                dayCell.style.backgroundColor = '#f8f9fa';
            }
            
            // 添加提示信息
            if (pnl !== 0) {
                dayCell.title = `${dateStr}: ${pnl > 0 ? '+' : ''}${pnl.toFixed(2)} USD`;
            }
            
            calendar.appendChild(dayCell);
        }

        container.appendChild(calendar);

        // 添加图例
        const legend = document.createElement('div');
        legend.style.display = 'flex';
        legend.style.justifyContent = 'center';
        legend.style.alignItems = 'center';
        legend.style.marginTop = '15px';
        legend.style.fontSize = '12px';
        legend.innerHTML = `
            <span style="margin-right: 10px;">亏损</span>
            <div style="width: 15px; height: 15px; background-color: #e74c3c; margin-right: 5px;"></div>
            <div style="width: 15px; height: 15px; background-color: #f8f9fa; margin-right: 5px; border: 1px solid #ddd;"></div>
            <div style="width: 15px; height: 15px; background-color: #27ae60; margin-right: 5px;"></div>
            <span>盈利</span>
        `;
        container.appendChild(legend);
    }

    // 创建小时盈亏热力图
    createHourlyHeatmap(canvasId, data) {
        const ctx = document.getElementById(canvasId);
        if (!ctx) return null;

        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
        }

        const hourlyData = data || {};
        const labels = Array.from({ length: 24 }, (_, i) => `${i}:00`);
        const series = {
            '周一': [], '周二': [], '周三': [], '周四': [], '周五': [], '周六': [], '周日': []
        };

        for (let day = 0; day < 7; day++) {
            for (let hour = 0; hour < 24; hour++) {
                const key = `${day}-${hour}`;
                const dayName = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'][day];
                series[dayName].push(hourlyData[key] ? hourlyData[key].pnl : 0);
            }
        }

        this.charts[canvasId] = new Chart(ctx, {
            type: 'heatmap',
            data: {
                labels: labels,
                datasets: Object.keys(series).map(day => ({
                    label: day,
                    data: series[day],
                }))
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                const pnl = context.dataset.data[context.dataIndex];
                                return `${context.dataset.label} ${context.label}: ${pnl.toFixed(2)} USD`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        type: 'category',
                        labels: Object.keys(series),
                        offset: true
                    },
                    x: {
                        type: 'category',
                        labels: labels,
                        position: 'top'
                    }
                }
            }
        });
    }

    // 销毁所有图表
    destroyAll() {
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        this.charts = {};
    }

    // 销毁特定图表
    destroy(canvasId) {
        if (this.charts[canvasId]) {
            this.charts[canvasId].destroy();
            delete this.charts[canvasId];
        }
    }
}

// 创建全局图表管理器实例
window.chartManager = new ChartManager();

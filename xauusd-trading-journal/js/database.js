// 数据库管理类 - 使用localStorage模拟数据库
class Database {
    constructor() {
        this.initDatabase();
    }

    // 初始化数据库
    initDatabase() {
        // 如果是第一次使用，初始化数据结构
        if (!localStorage.getItem('trades')) {
            localStorage.setItem('trades', JSON.stringify([]));
        }
        if (!localStorage.getItem('capital_movements')) {
            localStorage.setItem('capital_movements', JSON.stringify([]));
        }
        if (!localStorage.getItem('journal_entries')) {
            localStorage.setItem('journal_entries', JSON.stringify([]));
        }
        if (!localStorage.getItem('settings')) {
            localStorage.setItem('settings', JSON.stringify({
                initial_capital: 10000,
                currency: 'USD',
                created_at: new Date().toISOString()
            }));
        }
    }

    // 生成唯一ID
    generateId() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    }

    // 交易记录相关方法
    addTrade(trade) {
        const trades = this.getTrades();
        
        // 将逗号分隔的标签字符串转换为数组
        const tags = trade.tags ? trade.tags.split(',').map(tag => tag.trim()).filter(tag => tag) : [];

        const newTrade = {
            id: this.generateId(),
            trade_date: trade.trade_date,
            trade_type: trade.trade_type, // 'long' 或 'short'
            pnl: parseFloat(trade.pnl),
            position_size: parseFloat(trade.position_size) || null,
            entry_price: parseFloat(trade.entry_price) || null,
            exit_price: parseFloat(trade.exit_price) || null,
            entry_time: trade.entry_time || null,
            exit_time: trade.exit_time || null,
            stop_loss: parseFloat(trade.stop_loss) || null,
            take_profit: parseFloat(trade.take_profit) || null,
            commission: parseFloat(trade.commission) || 0,
            tags: tags, // 存储为数组
            notes: trade.notes || '',
            created_at: new Date().toISOString()
        };
        trades.push(newTrade);
        localStorage.setItem('trades', JSON.stringify(trades));
        return newTrade;
    }

    getTrades() {
        return JSON.parse(localStorage.getItem('trades') || '[]');
    }

    getTradeById(id) {
        const trades = this.getTrades();
        return trades.find(trade => trade.id === id);
    }

    updateTrade(id, updates) {
        const trades = this.getTrades();
        const index = trades.findIndex(trade => trade.id === id);
        if (index !== -1) {
            trades[index] = { ...trades[index], ...updates };
            localStorage.setItem('trades', JSON.stringify(trades));
            return trades[index];
        }
        return null;
    }

    deleteTrade(id) {
        const trades = this.getTrades();
        const filteredTrades = trades.filter(trade => trade.id !== id);
        localStorage.setItem('trades', JSON.stringify(filteredTrades));
        return true;
    }

    // 资金变动相关方法
    addCapitalMovement(movement) {
        const movements = this.getCapitalMovements();
        const newMovement = {
            id: this.generateId(),
            movement_date: movement.movement_date,
            movement_type: movement.movement_type, // 'deposit' 或 'withdrawal'
            amount: parseFloat(movement.amount),
            created_at: new Date().toISOString()
        };
        movements.push(newMovement);
        localStorage.setItem('capital_movements', JSON.stringify(movements));
        return newMovement;
    }

    getCapitalMovements() {
        return JSON.parse(localStorage.getItem('capital_movements') || '[]');
    }

    deleteCapitalMovement(id) {
        const movements = this.getCapitalMovements();
        const filteredMovements = movements.filter(movement => movement.id !== id);
        localStorage.setItem('capital_movements', JSON.stringify(filteredMovements));
        return true;
    }

    // 日志相关方法
    addJournalEntry(entry) {
        const entries = this.getJournalEntries();
        const existingIndex = entries.findIndex(e => e.entry_date === entry.entry_date);
        
        const journalEntry = {
            id: existingIndex !== -1 ? entries[existingIndex].id : this.generateId(),
            entry_date: entry.entry_date,
            content: entry.content,
            updated_at: new Date().toISOString()
        };

        if (existingIndex !== -1) {
            entries[existingIndex] = journalEntry;
        } else {
            entries.push(journalEntry);
        }
        
        localStorage.setItem('journal_entries', JSON.stringify(entries));
        return journalEntry;
    }

    getJournalEntries() {
        return JSON.parse(localStorage.getItem('journal_entries') || '[]');
    }

    getJournalByDate(date) {
        const entries = this.getJournalEntries();
        return entries.find(entry => entry.entry_date === date);
    }

    // 统计分析方法
    getAdvancedTradeStats(filters = {}) {
        let trades = this.getTrades();

        // 应用筛选器
        if (filters.startDate) {
            trades = trades.filter(t => t.trade_date >= filters.startDate);
        }
        if (filters.endDate) {
            trades = trades.filter(t => t.trade_date <= filters.endDate);
        }
        if (filters.tradeType && filters.tradeType !== 'all') {
            trades = trades.filter(t => t.trade_type === filters.tradeType);
        }
        if (filters.tag && filters.tag !== 'all') {
            trades = trades.filter(t => t.tags && t.tags.includes(filters.tag));
        }

        const totalTrades = trades.length;
        if (totalTrades === 0) {
            return { /* return zeroed-out stats */ };
        }

        const totalPnL = trades.reduce((sum, trade) => sum + trade.pnl, 0);
        const winningTrades = trades.filter(trade => trade.pnl > 0);
        const losingTrades = trades.filter(trade => trade.pnl < 0);

        const totalWins = winningTrades.reduce((sum, t) => sum + t.pnl, 0);
        const totalLosses = Math.abs(losingTrades.reduce((sum, t) => sum + t.pnl, 0));
        
        const winRate = totalTrades > 0 ? (winningTrades.length / totalTrades) * 100 : 0;
        const lossRate = 100 - winRate;

        const avgWin = winningTrades.length > 0 ? totalWins / winningTrades.length : 0;
        const avgLoss = losingTrades.length > 0 ? totalLosses / losingTrades.length : 0;

        const profitFactor = totalLosses > 0 ? totalWins / totalLosses : 0;
        const avgWinLossRatio = avgLoss > 0 ? avgWin / avgLoss : 0;
        const expectancy = totalTrades > 0 ? ((winRate / 100) * avgWin) - ((lossRate / 100) * avgLoss) : 0;

        // 策略表现分析
        const strategyPerformance = {};
        const hourlyPerformance = {}; // 新增：小时表现
        const allTags = [...new Set(trades.flatMap(t => t.tags))];

        // 初始化小时表现数据
        for (let day = 0; day < 7; day++) {
            for (let hour = 0; hour < 24; hour++) {
                hourlyPerformance[`${day}-${hour}`] = { pnl: 0, trades: 0 };
            }
        }

        trades.forEach(trade => {
            if (trade.entry_time) {
                const entryDate = new Date(`${trade.trade_date}T${trade.entry_time}`);
                const day = entryDate.getDay();
                const hour = entryDate.getHours();
                const key = `${day}-${hour}`;
                hourlyPerformance[key].pnl += trade.pnl;
                hourlyPerformance[key].trades++;
            }
        });

        allTags.forEach(tag => {
            const tagTrades = trades.filter(t => t.tags.includes(tag));
            const tagTotalTrades = tagTrades.length;
            const tagWinningTrades = tagTrades.filter(t => t.pnl > 0);
            const tagLosingTrades = tagTrades.filter(t => t.pnl < 0);
            const tagTotalWins = tagWinningTrades.reduce((sum, t) => sum + t.pnl, 0);
            const tagTotalLosses = Math.abs(tagLosingTrades.reduce((sum, t) => sum + t.pnl, 0));
            const tagWinRate = tagTotalTrades > 0 ? (tagWinningTrades.length / tagTotalTrades) * 100 : 0;
            const tagLossRate = 100 - tagWinRate;
            const tagAvgWin = tagWinningTrades.length > 0 ? tagTotalWins / tagWinningTrades.length : 0;
            const tagAvgLoss = tagLosingTrades.length > 0 ? tagTotalLosses / tagLosingTrades.length : 0;

            strategyPerformance[tag] = {
                totalTrades: tagTotalTrades,
                totalPnL: tagTotalWins - tagTotalLosses,
                winRate: tagWinRate,
                profitFactor: tagTotalLosses > 0 ? tagTotalWins / tagTotalLosses : 0,
                expectancy: tagTotalTrades > 0 ? ((tagWinRate / 100) * tagAvgWin) - ((tagLossRate / 100) * tagAvgLoss) : 0,
            };
        });


        return {
            totalTrades,
            totalPnL,
            winRate,
            profitFactor,
            expectancy,
            avgWinLossRatio,
            sharpeRatio: 0, // Placeholder
            avgHoldingTime: "N/A", // Placeholder
            strategyPerformance,
        };
    }

    getTradeStats() {
        const trades = this.getTrades();
        const movements = this.getCapitalMovements();
        const settings = JSON.parse(localStorage.getItem('settings'));

        if (trades.length === 0) {
            return {
                totalTrades: 0,
                totalPnL: 0,
                winRate: 0,
                avgWin: 0,
                avgLoss: 0,
                profitFactor: 0,
                longWinRate: 0,
                shortWinRate: 0,
                maxDrawdown: 0,
                currentBalance: settings.initial_capital
            };
        }

        const totalPnL = trades.reduce((sum, trade) => sum + trade.pnl, 0);
        const winningTrades = trades.filter(trade => trade.pnl > 0);
        const losingTrades = trades.filter(trade => trade.pnl < 0);
        
        const longTrades = trades.filter(trade => trade.trade_type === 'long');
        const shortTrades = trades.filter(trade => trade.trade_type === 'short');
        const longWins = longTrades.filter(trade => trade.pnl > 0);
        const shortWins = shortTrades.filter(trade => trade.pnl > 0);

        const totalCapitalMovements = movements.reduce((sum, movement) => {
            return sum + (movement.movement_type === 'deposit' ? movement.amount : -movement.amount);
        }, 0);

        // 计算资金曲线和最大回撤
        const { equityCurve, maxDrawdown } = this.calculateEquityCurve();

        return {
            totalTrades: trades.length,
            totalPnL: totalPnL,
            winRate: trades.length > 0 ? (winningTrades.length / trades.length * 100) : 0,
            avgWin: winningTrades.length > 0 ? winningTrades.reduce((sum, trade) => sum + trade.pnl, 0) / winningTrades.length : 0,
            avgLoss: losingTrades.length > 0 ? Math.abs(losingTrades.reduce((sum, trade) => sum + trade.pnl, 0) / losingTrades.length) : 0,
            profitFactor: losingTrades.length > 0 ? 
                Math.abs(winningTrades.reduce((sum, trade) => sum + trade.pnl, 0) / losingTrades.reduce((sum, trade) => sum + trade.pnl, 0)) : 
                winningTrades.length > 0 ? 999 : 0,
            longWinRate: longTrades.length > 0 ? (longWins.length / longTrades.length * 100) : 0,
            shortWinRate: shortTrades.length > 0 ? (shortWins.length / shortTrades.length * 100) : 0,
            maxDrawdown: maxDrawdown,
            currentBalance: settings.initial_capital + totalCapitalMovements + totalPnL,
            equityCurve: equityCurve
        };
    }

    // 计算资金曲线和最大回撤
    calculateEquityCurve() {
        const trades = this.getTrades().sort((a, b) => new Date(a.trade_date) - new Date(b.trade_date));
        const movements = this.getCapitalMovements().sort((a, b) => new Date(a.movement_date) - new Date(b.movement_date));
        const settings = JSON.parse(localStorage.getItem('settings'));
        
        let balance = settings.initial_capital;
        let peak = balance;
        let maxDrawdown = 0;
        const equityCurve = [{ date: new Date().toISOString().split('T')[0], balance: balance }];

        // 合并交易和资金变动，按日期排序
        const allEvents = [
            ...trades.map(t => ({ ...t, type: 'trade', date: t.trade_date })),
            ...movements.map(m => ({ ...m, type: 'movement', date: m.movement_date }))
        ].sort((a, b) => new Date(a.date) - new Date(b.date));

        allEvents.forEach(event => {
            if (event.type === 'trade') {
                balance += event.pnl;
            } else {
                balance += event.movement_type === 'deposit' ? event.amount : -event.amount;
            }

            if (balance > peak) {
                peak = balance;
            }

            const drawdown = (peak - balance) / peak * 100;
            if (drawdown > maxDrawdown) {
                maxDrawdown = drawdown;
            }

            equityCurve.push({ date: event.date, balance: balance });
        });

        return { equityCurve, maxDrawdown };
    }

    // 获取每日盈亏数据（用于日历热力图）
    getDailyPnL() {
        const trades = this.getTrades();
        const dailyPnL = {};

        trades.forEach(trade => {
            const date = trade.trade_date;
            if (!dailyPnL[date]) {
                dailyPnL[date] = 0;
            }
            dailyPnL[date] += trade.pnl;
        });

        return dailyPnL;
    }

    // 导出数据
    exportData() {
        return {
            trades: this.getTrades(),
            capital_movements: this.getCapitalMovements(),
            journal_entries: this.getJournalEntries(),
            settings: JSON.parse(localStorage.getItem('settings')),
            exported_at: new Date().toISOString()
        };
    }

    // 导入数据
    importData(data) {
        try {
            if (data.trades) localStorage.setItem('trades', JSON.stringify(data.trades));
            if (data.capital_movements) localStorage.setItem('capital_movements', JSON.stringify(data.capital_movements));
            if (data.journal_entries) localStorage.setItem('journal_entries', JSON.stringify(data.journal_entries));
            if (data.settings) localStorage.setItem('settings', JSON.stringify(data.settings));
            return true;
        } catch (error) {
            console.error('导入数据失败:', error);
            return false;
        }
    }
}

// 创建全局数据库实例
window.db = new Database();

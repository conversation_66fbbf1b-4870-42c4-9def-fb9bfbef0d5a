
// analysis.js

function renderAnalysisPage(container) {
    const allTrades = window.db.getTrades();
    // 修复tags处理，过滤掉undefined和null值
    const allTags = [...new Set(allTrades.flatMap(t => t.tags || []).filter(tag => tag && tag.trim()))];

    container.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">全局筛选器</h3>
            </div>
            <form id="analysis-filters">
                <div class="grid grid-4">
                    <div class="form-group">
                        <label class="form-label">日期范围 (开始)</label>
                        <input type="date" id="filter-start-date" class="form-input">
                    </div>
                    <div class="form-group">
                        <label class="form-label">日期范围 (结束)</label>
                        <input type="date" id="filter-end-date" class="form-input">
                    </div>
                    <div class="form-group">
                        <label class="form-label">交易类型</label>
                        <select id="filter-trade-type" class="form-select">
                            <option value="all">全部</option>
                            <option value="long">做多 (Long)</option>
                            <option value="short">做空 (Short)</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label class="form-label">策略/标签</label>
                        <select id="filter-tags" class="form-select">
                            <option value="all">全部</option>
                            ${allTags.map(tag => `<option value="${tag}">${tag}</option>`).join('')}
                        </select>
                    </div>
                </div>
            </form>
        </div>

        <div id="kpi-container">
            <!-- KPI指标将在这里动态渲染 -->
        </div>

        <div class="grid grid-1" style="margin-top: 30px;">
            <div class="card">
                <div class="card-header"><h3 class="card-title">策略绩效对比</h3></div>
                <div id="strategy-performance-container"></div>
            </div>
        </div>

        <div class="grid grid-1" style="margin-top: 30px;">
            <div class="card">
                <div class="card-header"><h3 class="card-title">交易时间分析</h3></div>
                <div id="hourly-heatmap-container" style="height: 400px;">
                    <p style="text-align: center; color: #7f8c8d; padding: 40px;">
                        时间热力图功能开发中，敬请期待...
                    </p>
                </div>
            </div>
        </div>
    `;

    // 绑定事件监听
    document.getElementById('analysis-filters').addEventListener('change', updateAnalysis);

    // 初始加载分析数据
    updateAnalysis();
}

function updateAnalysis() {
    const filters = {
        startDate: document.getElementById('filter-start-date').value,
        endDate: document.getElementById('filter-end-date').value,
        tradeType: document.getElementById('filter-trade-type').value,
        tag: document.getElementById('filter-tags').value,
    };

    const stats = window.db.getAdvancedTradeStats(filters);

    renderKPIs(stats);
    renderStrategyPerformance(stats.strategyPerformance);

    // 暂时注释掉热力图，因为Chart.js不支持heatmap类型
    // TODO: 实现自定义热力图或使用其他图表库
    // window.chartManager.createHourlyHeatmap('hourly-heatmap-container', stats.hourlyPerformance);
}

function renderKPIs(stats) {
    const container = document.getElementById('kpi-container');
    container.innerHTML = `
        <div class="grid grid-4" style="margin-top: 30px;">
            <div class="kpi-card">
                <div class="kpi-value">$${stats.totalPnL.toFixed(2)}</div>
                <div class="kpi-label">总净利润</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">${stats.profitFactor.toFixed(2)}</div>
                <div class="kpi-label">盈利因子</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">${stats.expectancy.toFixed(2)}</div>
                <div class="kpi-label">期望值</div>
            </div>
            <div class="kpi-card">
                <div class="kpi-value">${stats.winRate.toFixed(1)}%</div>
                <div class="kpi-label">胜率</div>
            </div>
        </div>
        <div class="card" style="margin-top: 20px;">
            <div class="card-header">
                <h3 class="card-title">详细统计</h3>
            </div>
            <div class="grid grid-4">
                <div><strong>交易笔数:</strong> ${stats.totalTrades}</div>
                <div><strong>平均盈亏比:</strong> ${stats.avgWinLossRatio.toFixed(2)}</div>
                <div><strong>平均持仓时间:</strong> ${stats.avgHoldingTime}</div>
                <div><strong>夏普比率:</strong> ${stats.sharpeRatio.toFixed(2)}</div>
            </div>
        </div>
    `;
}

function renderStrategyPerformance(strategyData) {
    const container = document.getElementById('strategy-performance-container');
    if (!strategyData || Object.keys(strategyData).length === 0) {
        container.innerHTML = '<p style="text-align: center; color: #7f8c8d; padding: 20px;">无策略数据</p>';
        return;
    }

    const tableRows = Object.entries(strategyData).map(([tag, stats]) => `
        <tr>
            <td><span class="tag">${tag}</span></td>
            <td>$${stats.totalPnL.toFixed(2)}</td>
            <td>${stats.winRate.toFixed(1)}%</td>
            <td>${stats.profitFactor.toFixed(2)}</td>
            <td>${stats.expectancy.toFixed(2)}</td>
            <td>${stats.totalTrades}</td>
        </tr>
    `).join('');

    container.innerHTML = `
        <div style="overflow-x: auto;">
            <table class="table">
                <thead>
                    <tr>
                        <th>策略/标签</th>
                        <th>净利润</th>
                        <th>胜率</th>
                        <th>盈利因子</th>
                        <th>期望值</th>
                        <th>笔数</th>
                    </tr>
                </thead>
                <tbody>
                    ${tableRows}
                </tbody>
            </table>
        </div>
    `;
}
